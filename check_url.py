import mlflow
import requests
import time

# Set tracking URI
tracking_uri = "https://mlfow-custom-dev.aimlops.itg.ti.com/"
print(f"Testing connection to: {tracking_uri}")

# Test basic connectivity first
try:
    start = time.time()
    response = requests.get(tracking_uri, timeout=10, verify=False)
    print(f"Server response: {response.status_code}")
    print(f"Response time: {time.time() - start:.2f}s")
except Exception as e:
    print(f"Connection error: {e}")

# Try setting tracking URI with timeout
try:
    mlflow.set_tracking_uri(tracking_uri)
    print(f"Successfully set tracking URI to: {mlflow.get_tracking_uri()}")
except Exception as e:
    print(f"Error setting tracking URI: {e}")


# # Try listing experiments (with timeout protection)
# try:
#     print("Attempting to list experiments (10s timeout)...")
#     start = time.time()
#     # Use a separate thread or process to prevent hanging
#     import concurrent.futures
#     with concurrent.futures.ThreadPoolExecutor() as executor:
#         future = executor.submit(mlflow.list_experiments())
#         experiments = future.result(timeout=10)
#     print(f"Found {len(experiments)} experiments in {time.time() - start:.2f}s")
# except concurrent.futures.TimeoutError:
#     print("Operation timed out - server not responding")
# except Exception as e:
    print(f"Error listing experiments: {e}")