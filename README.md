# MLflow on Kubernetes

This repository contains configuration files and examples for deploying MLflow on a Kubernetes cluster.

## Overview

MLflow is an open-source platform for managing the end-to-end machine learning lifecycle. This deployment includes:

- MLflow Tracking Server
- PostgreSQL database for backend storage
- Persistent storage for artifacts
- Kubernetes services for accessing MLflow

## Prerequisites

- Kubernetes cluster (e.g., minikube, EKS, GKE, AKS)
- kubectl installed and configured
- Basic understanding of Kubernetes concepts

## Deployment Instructions

### 1. Deploy MLflow to Kubernetes

```bash
# Apply the Kubernetes configuration
kubectl apply -f mlflow-kubernetes-deployment.yaml

# Verify the deployment
kubectl get all -n mlflow
```

### 2. Access the MLflow UI

#### Option 1: Port Forwarding (for development/testing)

```bash
# Forward the MLflow service port to your local machine
kubectl port-forward -n mlflow svc/mlflow-service 5000:5000
```

Then access MLflow at: http://localhost:5000

#### Option 2: LoadBalancer (for production)

If you've enabled the LoadBalancer service in the configuration:

```bash
# Get the external IP of the LoadBalancer
kubectl get svc mlflow-lb -n mlflow
```

Then access MLflow at: http://<EXTERNAL-IP>

### 3. Using MLflow with the Kubernetes Deployment

Set the `MLFLOW_TRACKING_URI` environment variable to point to your MLflow server:

```bash
export MLFLOW_TRACKING_URI="http://<mlflow-service-address>:5000"
```

## Example Usage

The `mlflow_kubernetes_client.py` script demonstrates how to use MLflow with this Kubernetes deployment:

```bash
# Install required packages
pip install mlflow scikit-learn numpy

# Run the example script
python mlflow_kubernetes_client.py
```

This script:
1. Connects to the MLflow server running on Kubernetes
2. Trains a simple machine learning model
3. Logs parameters, metrics, and artifacts to MLflow
4. Demonstrates how to load a model from MLflow

## Configuration Details

### Storage Options

The default configuration uses:
- PostgreSQL for metadata storage
- Local persistent volumes for artifact storage

For production deployments, consider using:
- Amazon S3, Azure Blob Storage, or Google Cloud Storage for artifacts
- Managed database services for metadata

### Scaling

For higher availability and performance:
1. Increase the number of MLflow replicas
2. Set up a database cluster for PostgreSQL
3. Configure appropriate resource limits

## Troubleshooting

### Common Issues

1. **Cannot connect to MLflow server**:
   - Check if the pods are running: `kubectl get pods -n mlflow`
   - Check pod logs: `kubectl logs -n mlflow <pod-name>`

2. **Database connection issues**:
   - Verify PostgreSQL is running: `kubectl get pods -n mlflow | grep postgres`
   - Check PostgreSQL logs: `kubectl logs -n mlflow <postgres-pod-name>`

3. **Artifact storage problems**:
   - Check persistent volume claims: `kubectl get pvc -n mlflow`
   - Verify volume mounts in the MLflow pod

## Additional Resources

- [MLflow Documentation](https://mlflow.org/docs/latest/index.html)
- [Kubernetes Documentation](https://kubernetes.io/docs/home/<USER>
- [MLflow on Kubernetes Tutorial](https://mlflow.org/docs/latest/deployment/deploy-model-to-kubernetes/tutorial/)
