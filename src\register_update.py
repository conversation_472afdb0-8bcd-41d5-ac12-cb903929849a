import mlflow
from mlflow.tracking import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

def register_model_with_metadata(model_name, run_id, version=None, tags=None, description=None, alias=None, created_by=None):
    """
    Registers an MLflow model and updates it with optional tags, description, and alias.

    Args:
        model_name (str): The name to assign to the registered model.
        run_id (str): The MLflow run ID associated with the model.
        version (str, optional): The version of the model to update or create. Defaults to None.
        tags (dict, optional): A dictionary of tags to add to the registered model. Defaults to None.
        description (str, optional): A description for the registered model. Defaults to None.
        alias (str, optional): An alias for the model. Defaults to None.
        created_by (str, optional): The creator of the model. Defaults to None.
    """

    model_uri = f'runs:/{run_id}/model'
    client = MlflowClient()

    try:
        if version:
            # Check if the specified version exists
            try:
                model_version = client.get_model_version(name=model_name, version=version)
                print(f"Version {version} of model '{model_name}' exists. Updating it.")
            except mlflow.exceptions.RestException:
                # If version doesn't exist, create a new version
                print(f"Version {version} of model '{model_name}' does not exist. Creating a new version.")
                registered_model_version = mlflow.register_model(
                    model_uri=model_uri,
                    name=model_name,
                    tags=tags if tags else {}
                )
                version = registered_model_version.version
        else:
            # Register a new version if no version is specified
            registered_model_version = mlflow.register_model(
                model_uri=model_uri,
                name=model_name,
                tags=tags if tags else {}
            )
            version = registered_model_version.version

        # Update the description (if provided)
        if description:
            client.update_model_version(
                name=model_name,
                version=version,
                description=description
            )

        # Add alias directly (if provided)
        if alias:
            client.set_registered_model_alias(
            name=model_name,
            alias=alias,
            version=version
            )

        # Add created_by directly (if provided)
        if created_by:
            client.update_registered_model(
                name=model_name,
                description=f"Created by: {created_by}\n{client.get_registered_model(name=model_name).description}"
            )

        print(f"Model '{model_name}' registered/updated successfully with version '{version}' and run_id '{run_id}'.")

    except Exception as e:
        print(f"Error registering/updating model: {e}")


if __name__ == '__main__':
    model_name = 'Random Forest'
    run_id = input('Please type RunID: ')
    version = input('Please type version (or leave blank for new version): ')

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    default_tags = {
        "model_type": "Random Forest Classifier",
        "timestamp": timestamp,
        "data_version": "v1.0",
        "purpose": "Anomaly Detection"
    }

    default_description = f"""
    Random Forest Model for Anomaly Detection
    Created on: {timestamp}
    Model trained on imbalanced binary classification dataset with 1000 samples and 10 features.
    """

    register_model_with_metadata(
        model_name=model_name,
        run_id=run_id,
        version=version if version else None,
        tags=default_tags,
        description=default_description,
        alias="initial_model",
        created_by="Lucifer4073"
    )