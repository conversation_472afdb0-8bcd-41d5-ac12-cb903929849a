"""
MLflow Model Loader

This module provides utilities for loading MLflow models from various sources:
- Directly from experiment runs using run_id or model_uri
- From the model registry using name, version, stage, or alias

The module handles different model types automatically and provides comprehensive
error handling for various failure scenarios.
"""

import logging
from typing import Union, Dict, Any
import mlflow
from mlflow.tracking import MlflowClient
from mlflow.exceptions import MlflowException

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelNotFoundError(Exception):
    """Exception raised when a model cannot be found."""
    pass

class ModelLoadError(Exception):
    """Exception raised when a model cannot be loaded."""
    pass

def load_model(
    model_identifier: str = None,
    run_id: str = None,
    model_name: str = None,
    model_version: Union[str, int] = None,
    model_stage: str = None,
    model_alias: str = None,
    artifact_path: str = "model",
    tracking_uri: str = None,
    **kwargs
) -> Any:
    """
    Load an MLflow model from various sources with flexible options.

    This function provides a unified interface to load models from:
    - Experiment runs (using run_id)
    - Model URIs (runs:/<run_id>/<artifact_path>)
    - Model Registry (using name, version, stage, or alias)

    Args:
        model_identifier: A direct model URI or identifier (e.g., "runs:/<run_id>/model" or "models:/<name>/<version>")
        run_id: The run ID from which to load the model
        model_name: The name of the registered model to load
        model_version: The version of the registered model to load
        model_stage: The stage of the registered model to load (e.g., "Production", "Staging")
        model_alias: The alias of the registered model to load
        artifact_path: The path within the run's artifacts where the model is stored (default: "model")
        tracking_uri: The MLflow tracking URI to use (if None, uses the current tracking URI)
        **kwargs: Additional keyword arguments to pass to the mlflow.<flavor>.load_model function

    Returns:
        The loaded model

    Raises:
        ModelNotFoundError: If the model cannot be found
        ModelLoadError: If the model cannot be loaded
        ValueError: If the input parameters are invalid or insufficient
    """
    # Set tracking URI if provided
    if tracking_uri:
        mlflow.set_tracking_uri(tracking_uri)

    try:
        # Case 1: Direct model_identifier is provided
        if model_identifier:
            logger.info(f"Loading model from identifier: {model_identifier}")
            return _load_model_from_uri(model_identifier, **kwargs)

        # Case 2: Loading from run_id
        if run_id:
            logger.info(f"Loading model from run_id: {run_id}, artifact_path: {artifact_path}")
            return _load_from_run(run_id, artifact_path, **kwargs)

        # Case 3: Loading from registry
        if model_name:
            logger.info(f"Loading model from registry: {model_name}")
            return _load_from_registry(model_name, model_version, model_stage, model_alias, **kwargs)

        # If we get here, we don't have enough information to load a model
        raise ValueError(
            "Insufficient information to load model. Please provide either "
            "model_identifier, run_id, or model_name (with optional version/stage/alias)."
        )

    except MlflowException as e:
        if "RESOURCE_DOES_NOT_EXIST" in str(e) or "Not found" in str(e):
            raise ModelNotFoundError(f"Model not found: {str(e)}")
        else:
            raise ModelLoadError(f"Error loading model: {str(e)}")

    except Exception as e:
        raise ModelLoadError(f"Unexpected error loading model: {str(e)}")

def _load_model_from_uri(model_uri: str, **kwargs) -> Any:
    """
    Load a model from a URI.

    Args:
        model_uri: The URI of the model to load
        **kwargs: Additional keyword arguments to pass to the mlflow.<flavor>.load_model function

    Returns:
        The loaded model

    Raises:
        ModelLoadError: If the model cannot be loaded
    """
    try:
        # MLflow will automatically determine the model type and use the appropriate flavor
        return mlflow.pyfunc.load_model(model_uri, **kwargs)
    except Exception as e:
        raise ModelLoadError(f"Failed to load model from URI '{model_uri}': {str(e)}")

def _load_from_run(run_id: str, artifact_path: str = "model", **kwargs) -> Any:
    """
    Load a model from a run.

    Args:
        run_id: The run ID from which to load the model
        artifact_path: The path within the run's artifacts where the model is stored
        **kwargs: Additional keyword arguments to pass to the mlflow.<flavor>.load_model function

    Returns:
        The loaded model

    Raises:
        ModelNotFoundError: If the run or model cannot be found
        ModelLoadError: If the model cannot be loaded
    """
    try:
        # Verify that the run exists
        client = MlflowClient()
        try:
            # Just check if the run exists, we don't need to store it
            client.get_run(run_id)
        except MlflowException as e:
            if "RESOURCE_DOES_NOT_EXIST" in str(e):
                raise ModelNotFoundError(f"Run with ID '{run_id}' not found")
            raise

        # Construct the model URI
        model_uri = f"runs:/{run_id}/{artifact_path}"
        logger.info(f"Loading model from URI: {model_uri}")

        # Load the model
        return _load_model_from_uri(model_uri, **kwargs)

    except ModelNotFoundError:
        raise
    except Exception as e:
        raise ModelLoadError(f"Failed to load model from run '{run_id}': {str(e)}")

def _load_from_registry(
    model_name: str,
    version: Union[str, int] = None,
    stage: str = None,
    alias: str = None,
    **kwargs
) -> Any:
    """
    Load a model from the registry.

    Args:
        model_name: The name of the registered model to load
        version: The version of the registered model to load
        stage: The stage of the registered model to load (e.g., "Production", "Staging")
        alias: The alias of the registered model to load
        **kwargs: Additional keyword arguments to pass to the mlflow.<flavor>.load_model function

    Returns:
        The loaded model

    Raises:
        ModelNotFoundError: If the model cannot be found
        ModelLoadError: If the model cannot be loaded
        ValueError: If multiple specifiers (version, stage, alias) are provided
    """
    # Check that only one of version, stage, or alias is provided
    specifiers = [s for s in [version, stage, alias] if s is not None]
    if len(specifiers) > 1:
        raise ValueError(
            "Only one of version, stage, or alias should be provided. "
            f"Got version={version}, stage={stage}, alias={alias}"
        )

    try:
        client = MlflowClient()

        # Verify that the model exists in the registry
        try:
            client.get_registered_model(model_name)
        except MlflowException as e:
            if "RESOURCE_DOES_NOT_EXIST" in str(e):
                raise ModelNotFoundError(f"Model '{model_name}' not found in registry")
            raise

        # Construct the model URI based on the provided specifier
        if version is not None:
            model_uri = f"models:/{model_name}/{version}"
            # Verify that the version exists
            try:
                client.get_model_version(model_name, str(version))
            except MlflowException as e:
                if "RESOURCE_DOES_NOT_EXIST" in str(e):
                    raise ModelNotFoundError(f"Version {version} of model '{model_name}' not found")
                raise

        elif stage is not None:
            # Validate stage
            valid_stages = ["Production", "Staging", "Archived", "None"]
            if stage not in valid_stages:
                raise ValueError(f"Invalid stage '{stage}'. Must be one of {valid_stages}")

            model_uri = f"models:/{model_name}/{stage}"

            # Verify that a version with this stage exists
            versions = client.get_latest_versions(model_name, stages=[stage])
            if not versions:
                raise ModelNotFoundError(f"No version of model '{model_name}' found in stage '{stage}'")

        elif alias is not None:
            model_uri = f"models:/{model_name}@{alias}"

            # Verify that a version with this alias exists
            try:
                client.get_model_version_by_alias(model_name, alias)
            except MlflowException as e:
                if "RESOURCE_DOES_NOT_EXIST" in str(e):
                    raise ModelNotFoundError(f"No version of model '{model_name}' found with alias '{alias}'")
                raise

        else:
            # If no specifier is provided, use the latest version
            versions = client.get_latest_versions(model_name)
            if not versions:
                raise ModelNotFoundError(f"No versions found for model '{model_name}'")

            latest_version = versions[0].version
            model_uri = f"models:/{model_name}/{latest_version}"

        logger.info(f"Loading model from registry URI: {model_uri}")

        # Load the model
        return _load_model_from_uri(model_uri, **kwargs)

    except ModelNotFoundError:
        raise
    except ValueError:
        raise
    except Exception as e:
        raise ModelLoadError(f"Failed to load model '{model_name}' from registry: {str(e)}")

def get_model_info(
    model_identifier: str = None,
    run_id: str = None,
    model_name: str = None,
    model_version: Union[str, int] = None,
    model_stage: str = None,
    model_alias: str = None,
    tracking_uri: str = None
) -> Dict[str, Any]:
    """
    Get information about a model without loading it.

    Args:
        model_identifier: A direct model URI or identifier
        run_id: The run ID from which to get model info
        model_name: The name of the registered model
        model_version: The version of the registered model
        model_stage: The stage of the registered model
        model_alias: The alias of the registered model
        tracking_uri: The MLflow tracking URI to use

    Returns:
        A dictionary containing information about the model

    Raises:
        ModelNotFoundError: If the model cannot be found
        ValueError: If the input parameters are invalid or insufficient
    """
    # Set tracking URI if provided
    if tracking_uri:
        mlflow.set_tracking_uri(tracking_uri)

    client = MlflowClient()

    try:
        # Case 1: Direct model_identifier is provided
        if model_identifier:
            # Parse the URI to determine the type
            if model_identifier.startswith("runs:/"):
                # Extract run_id and artifact_path
                parts = model_identifier.replace("runs:/", "").split("/")
                run_id = parts[0]
                artifact_path = "/".join(parts[1:]) if len(parts) > 1 else "model"

                return _get_run_model_info(client, run_id, artifact_path)

            elif model_identifier.startswith("models:/"):
                # Extract model_name and version/stage/alias
                parts = model_identifier.replace("models:/", "").split("/")
                model_name = parts[0]

                if len(parts) > 1:
                    # Check if it's a version, stage, or alias
                    if "@" in model_name:
                        model_name, alias = model_name.split("@")
                        return _get_registry_model_info(client, model_name, alias=alias)
                    else:
                        specifier = parts[1]
                        # Try to parse as version (integer)
                        try:
                            version = int(specifier)
                            return _get_registry_model_info(client, model_name, version=version)
                        except ValueError:
                            # If not a version, assume it's a stage
                            return _get_registry_model_info(client, model_name, stage=specifier)
                else:
                    # Just model name, get latest version
                    return _get_registry_model_info(client, model_name)

            else:
                raise ValueError(f"Invalid model identifier format: {model_identifier}")

        # Case 2: Getting info from run_id
        if run_id:
            artifact_path = "model"  # Default artifact path
            return _get_run_model_info(client, run_id, artifact_path)

        # Case 3: Getting info from registry
        if model_name:
            return _get_registry_model_info(client, model_name, model_version, model_stage, model_alias)

        # If we get here, we don't have enough information
        raise ValueError(
            "Insufficient information to get model info. Please provide either "
            "model_identifier, run_id, or model_name (with optional version/stage/alias)."
        )

    except MlflowException as e:
        if "RESOURCE_DOES_NOT_EXIST" in str(e) or "Not found" in str(e):
            raise ModelNotFoundError(f"Model not found: {str(e)}")
        else:
            raise ValueError(f"Error getting model info: {str(e)}")

def _get_run_model_info(client: MlflowClient, run_id: str, artifact_path: str) -> Dict[str, Any]:
    """Get information about a model in a run."""
    # Verify that the run exists
    try:
        run = client.get_run(run_id)
    except MlflowException as e:
        if "RESOURCE_DOES_NOT_EXIST" in str(e):
            raise ModelNotFoundError(f"Run with ID '{run_id}' not found")
        raise

    # Check if the model exists in the run
    artifacts = client.list_artifacts(run_id, artifact_path)
    if not any(artifact.path.endswith("MLmodel") for artifact in artifacts):
        raise ModelNotFoundError(f"No model found at path '{artifact_path}' in run '{run_id}'")

    # Get model info
    return {
        "source": "run",
        "run_id": run_id,
        "artifact_path": artifact_path,
        "run_info": run.info.to_dictionary(),
        "model_uri": f"runs:/{run_id}/{artifact_path}"
    }

def _get_registry_model_info(
    client: MlflowClient,
    model_name: str,
    version: Union[str, int] = None,
    stage: str = None,
    alias: str = None
) -> Dict[str, Any]:
    """Get information about a model in the registry."""
    # Verify that the model exists in the registry
    try:
        # Just check if the model exists, we don't need to store it
        client.get_registered_model(model_name)
    except MlflowException as e:
        if "RESOURCE_DOES_NOT_EXIST" in str(e):
            raise ModelNotFoundError(f"Model '{model_name}' not found in registry")
        raise

    # Determine which version to get info for
    model_version = None

    if version is not None:
        # Get specific version
        try:
            model_version = client.get_model_version(model_name, str(version))
        except MlflowException as e:
            if "RESOURCE_DOES_NOT_EXIST" in str(e):
                raise ModelNotFoundError(f"Version {version} of model '{model_name}' not found")
            raise

    elif stage is not None:
        # Get latest version in stage
        versions = client.get_latest_versions(model_name, stages=[stage])
        if not versions:
            raise ModelNotFoundError(f"No version of model '{model_name}' found in stage '{stage}'")
        model_version = versions[0]

    elif alias is not None:
        # Get version with alias
        try:
            model_version = client.get_model_version_by_alias(model_name, alias)
        except MlflowException as e:
            if "RESOURCE_DOES_NOT_EXIST" in str(e):
                raise ModelNotFoundError(f"No version of model '{model_name}' found with alias '{alias}'")
            raise

    else:
        # Get latest version
        versions = client.get_latest_versions(model_name)
        if not versions:
            raise ModelNotFoundError(f"No versions found for model '{model_name}'")
        model_version = versions[0]

    # Construct model URI
    if stage:
        model_uri = f"models:/{model_name}/{stage}"
    elif alias:
        model_uri = f"models:/{model_name}@{alias}"
    else:
        model_uri = f"models:/{model_name}/{model_version.version}"

    # Return model info
    return {
        "source": "registry",
        "model_name": model_name,
        "model_version": model_version.version,
        "model_stage": model_version.current_stage,
        "run_id": model_version.run_id,
        "model_uri": model_uri,
        "creation_timestamp": model_version.creation_timestamp,
        "last_updated_timestamp": model_version.last_updated_timestamp,
        "description": model_version.description,
        "tags": {tag.key: tag.value for tag in client.get_model_version_tags(model_name, model_version.version)}
    }