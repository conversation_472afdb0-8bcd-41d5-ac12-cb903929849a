import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, random_split
from torchvision import datasets, transforms
import mlflow
from src.mlflow_pytorch_utils import log_pytorch_model


# Define a CNN model for MNIST classification
class MNISTClassifier(nn.Module):
    def __init__(self):
        super(MNISTClassifier, self).__init__()
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, stride=1, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
        self.fc1 = nn.Linear(64 * 7 * 7, 128)
        self.fc2 = nn.Linear(128, 10)
        self.dropout = nn.Dropout(0.25)
    
    def forward(self, x):
        # Input: [batch_size, 1, 28, 28]
        x = F.relu(self.conv1(x))  # [batch_size, 32, 28, 28]
        x = self.pool(x)  # [batch_size, 32, 14, 14]
        x = F.relu(self.conv2(x))  # [batch_size, 64, 14, 14]
        x = self.pool(x)  # [batch_size, 64, 7, 7]
        x = x.view(-1, 64 * 7 * 7)  # [batch_size, 64 * 7 * 7]
        x = F.relu(self.fc1(x))  # [batch_size, 128]
        x = self.dropout(x)
        x = self.fc2(x)  # [batch_size, 10]
        return x


def train(model, train_loader, optimizer, criterion, device, epoch):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        
        # Forward pass
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        
        # Backward and optimize
        loss.backward()
        optimizer.step()
        
        # Track statistics
        running_loss += loss.item()
        _, predicted = output.max(1)
        total += target.size(0)
        correct += predicted.eq(target).sum().item()
        
        if batch_idx % 100 == 0:
            print(f'Epoch: {epoch}, Batch: {batch_idx}, Loss: {loss.item():.4f}, '
                  f'Accuracy: {100. * correct / total:.2f}%')
    
    train_loss = running_loss / len(train_loader)
    train_acc = 100. * correct / total
    
    return train_loss, train_acc


def validate(model, val_loader, criterion, device):
    model.eval()
    val_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            val_loss += criterion(output, target).item()
            _, predicted = output.max(1)
            total += target.size(0)
            correct += predicted.eq(target).sum().item()
    
    val_loss /= len(val_loader)
    val_acc = 100. * correct / total
    
    return val_loss, val_acc


def main():
    # Set random seed for reproducibility
    torch.manual_seed(42)
    
    # Check if CUDA is available
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Define hyperparameters
    batch_size = 64
    learning_rate = 0.001
    num_epochs = 5
    
    # Create model parameters dictionary
    model_params = {
        'batch_size': batch_size,
        'learning_rate': learning_rate,
        'num_epochs': num_epochs,
        'optimizer': 'Adam',
        'model_type': 'CNN',
        'dataset': 'MNIST'
    }
    
    # Load MNIST dataset
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))
    ])
    
    train_dataset = datasets.MNIST('./data', train=True, download=True, transform=transform)
    
    # Split into train and validation sets
    train_size = int(0.8 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = random_split(train_dataset, [train_size, val_size])
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Create model
    model = MNISTClassifier().to(device)
    
    # Define loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    # Set up MLflow experiment
    experiment_name = "MNIST_Classification"
    mlflow.set_experiment(experiment_name)
    
    # Start MLflow run
    with mlflow.start_run(run_name="cnn_classifier") as run:
        # Train the model
        for epoch in range(num_epochs):
            train_loss, train_acc = train(model, train_loader, optimizer, criterion, device, epoch)
            val_loss, val_acc = validate(model, val_loader, criterion, device)
            
            # Log metrics to MLflow
            mlflow.log_metric("train_loss", train_loss, step=epoch)
            mlflow.log_metric("train_accuracy", train_acc, step=epoch)
            mlflow.log_metric("val_loss", val_loss, step=epoch)
            mlflow.log_metric("val_accuracy", val_acc, step=epoch)
            
            print(f'Epoch: {epoch}, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '
                  f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        # Create input example for model signature
        # Get a batch from the validation set for the input example
        dataiter = iter(val_loader)
        images, _ = next(dataiter)
        input_example = images[:1].to(device)  # Just use one image
        
        # Log the model to MLflow using our utility function
        # Note: We're using the active run, so we don't need to specify experiment_name or run_name
        log_pytorch_model(
            model=model,
            model_params=model_params,
            input_example=input_example,
            tags={"model_type": "classification", "framework": "pytorch", "dataset": "MNIST"},
            auto_end_run=False  # Don't end the run since we're in a with block
        )
        
        run_id = run.info.run_id
    
    print(f"Model logged to MLflow with run_id: {run_id}")
    
    # Load the model back
    loaded_model = mlflow.pytorch.load_model(f"runs:/{run_id}/model")
    loaded_model.to(device)
    
    # Make predictions with the loaded model
    with torch.no_grad():
        # Get a batch from the validation set
        dataiter = iter(val_loader)
        images, labels = next(dataiter)
        images, labels = images.to(device), labels.to(device)
        
        # Get predictions
        outputs = loaded_model(images)
        _, predicted = torch.max(outputs, 1)
        
        # Print results
        print("Predictions from loaded model:")
        print(f"Predicted: {' '.join('%d' % predicted[j] for j in range(5))}")
        print(f"Actual:    {' '.join('%d' % labels[j] for j in range(5))}")


if __name__ == "__main__":
    main()
