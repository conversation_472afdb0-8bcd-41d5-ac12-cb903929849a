"""
Example script demonstrating how to use the model_loader module.

This script shows various ways to load models using the model_loader module:
- Loading from experiment runs
- Loading from the model registry
- Error handling
"""

import mlflow
import logging
from src.model_loader import load_model, get_model_info, ModelNotFoundError, ModelLoadError

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_load_from_run():
    """Example of loading a model from a run."""
    try:
        # Replace with an actual run_id from your MLflow tracking server
        run_id = "YOUR_RUN_ID"
        
        # Get information about the model without loading it
        model_info = get_model_info(run_id=run_id)
        logger.info(f"Model info: {model_info}")
        
        # Load the model
        model = load_model(run_id=run_id)
        logger.info(f"Successfully loaded model from run: {run_id}")
        
        # Use the model for predictions
        # Example: predictions = model.predict(data)
        
        return model
    except ModelNotFoundError as e:
        logger.error(f"Model not found: {e}")
    except ModelLoadError as e:
        logger.error(f"Error loading model: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    
    return None

def example_load_from_registry():
    """Example of loading a model from the registry."""
    try:
        # Replace with an actual model name from your MLflow model registry
        model_name = "YOUR_MODEL_NAME"
        
        # 1. Load the latest version
        model = load_model(model_name=model_name)
        logger.info(f"Successfully loaded latest version of model: {model_name}")
        
        # 2. Load a specific version
        model_version = "1"  # Replace with an actual version
        model = load_model(model_name=model_name, model_version=model_version)
        logger.info(f"Successfully loaded version {model_version} of model: {model_name}")
        
        # 3. Load from a specific stage
        model_stage = "Production"  # One of: "Production", "Staging", "Archived", "None"
        model = load_model(model_name=model_name, model_stage=model_stage)
        logger.info(f"Successfully loaded {model_stage} version of model: {model_name}")
        
        # 4. Load using an alias
        model_alias = "champion"  # Replace with an actual alias
        model = load_model(model_name=model_name, model_alias=model_alias)
        logger.info(f"Successfully loaded model with alias '{model_alias}': {model_name}")
        
        return model
    except ModelNotFoundError as e:
        logger.error(f"Model not found: {e}")
    except ModelLoadError as e:
        logger.error(f"Error loading model: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    
    return None

def example_load_with_direct_uri():
    """Example of loading a model using a direct URI."""
    try:
        # 1. Load from a run URI
        run_uri = "runs:/YOUR_RUN_ID/model"  # Replace with an actual run ID
        model = load_model(model_identifier=run_uri)
        logger.info(f"Successfully loaded model from URI: {run_uri}")
        
        # 2. Load from a registry URI
        registry_uri = "models:/YOUR_MODEL_NAME/Production"  # Replace with actual values
        model = load_model(model_identifier=registry_uri)
        logger.info(f"Successfully loaded model from URI: {registry_uri}")
        
        return model
    except ModelNotFoundError as e:
        logger.error(f"Model not found: {e}")
    except ModelLoadError as e:
        logger.error(f"Error loading model: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    
    return None

def example_error_handling():
    """Example of error handling when loading models."""
    # 1. Try to load a non-existent run
    try:
        load_model(run_id="non_existent_run_id")
    except ModelNotFoundError as e:
        logger.error(f"Expected error - Model not found: {e}")
    
    # 2. Try to load a non-existent model from registry
    try:
        load_model(model_name="non_existent_model")
    except ModelNotFoundError as e:
        logger.error(f"Expected error - Model not found: {e}")
    
    # 3. Try to load with invalid parameters
    try:
        load_model(model_name="some_model", model_version="1", model_stage="Production")
    except ValueError as e:
        logger.error(f"Expected error - Invalid parameters: {e}")
    
    # 4. Try to load without any parameters
    try:
        load_model()
    except ValueError as e:
        logger.error(f"Expected error - Missing parameters: {e}")

if __name__ == "__main__":
    # Set your MLflow tracking URI if needed
    # mlflow.set_tracking_uri("http://localhost:5000")
    
    logger.info("Running model loader examples...")
    
    # Uncomment the examples you want to run
    # example_load_from_run()
    # example_load_from_registry()
    # example_load_with_direct_uri()
    example_error_handling()
    
    logger.info("Finished running examples.")
