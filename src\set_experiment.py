import mlflow
import mlflow.sklearn
import mlflow.xgboost
import torch
import torch.nn as nn
# import tensorflow as tf
import random
from sklearn.metrics import classification_report
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
import numpy as np

def run_torch_experiment(models, experiment_name=None, uri="http://127.0.0.1:5000/", num_epochs=10, learning_rate=0.001):
    """
    Sets up and runs an MLflow experiment for PyTorch models.

    Args:
        models (list): A list of tuples, where each tuple contains:
            - model_name (str): The name of the model.
            - params (dict): A dictionary of model parameters.
            - model (torch.nn.Module): The PyTorch model.
            - train_set (tuple): A tuple containing training data (X_train, y_train).
            - test_set (tuple): A tuple containing testing data (X_test, y_test).
        experiment_name (str, optional): The name of the MLflow experiment.
            Defaults to "torch_exp_{random_value}".
        uri (str, optional): The tracking URI for MLflow.
            Defaults to "http://127.0.0.1:5000/".
        num_epochs (int, optional): Number of training epochs.
            Defaults to 10.
        learning_rate (float, optional): Learning rate for optimizer.
            Defaults to 0.001.
    """
    if experiment_name is None:
        experiment_name = f"torch_exp_{random.randint(1, 1000)}"

    mlflow.set_experiment(experiment_name)
    mlflow.set_tracking_uri(uri=uri)

    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    for i, element in enumerate(models):
        model_name = element[0]
        params = element[1]
        model = element[2]
        train_set = element[3]
        test_set = element[4]

        X_train, y_train = train_set
        X_test, y_test = test_set

        # Move model and data to device
        model = model.to(device)
        X_train = X_train.to(device)
        y_train = y_train.to(device)
        X_test = X_test.to(device)
        y_test = y_test.to(device)

        # Define loss function and optimizer
        criterion = torch.nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)

        # Training loop
        with mlflow.start_run(run_name=model_name):
            # Log parameters
            all_params = {**params, "learning_rate": learning_rate, "num_epochs": num_epochs}
            mlflow.log_params(all_params)

            # Training loop
            for epoch in range(num_epochs):
                model.train()

                # Forward pass
                outputs = model(X_train)
                loss = criterion(outputs, y_train)

                # Backward and optimize
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                # Log training metrics for this epoch
                mlflow.log_metric("train_loss", loss.item(), step=epoch)

                # Evaluate on test set
                model.eval()
                with torch.no_grad():
                    test_outputs = model(X_test)
                    test_loss = criterion(test_outputs, y_test)

                    # Calculate accuracy
                    _, predicted = torch.max(test_outputs.data, 1)
                    total = y_test.size(0)
                    correct = (predicted == y_test).sum().item()
                    accuracy = correct / total

                    # Log test metrics
                    mlflow.log_metric("test_loss", test_loss.item(), step=epoch)
                    mlflow.log_metric("test_accuracy", accuracy, step=epoch)

            # Final evaluation
            model.eval()
            with torch.no_grad():
                test_outputs = model(X_test)
                _, predicted = torch.max(test_outputs.data, 1)

                # Convert to numpy for classification report
                y_true = y_test.cpu().numpy()
                y_pred = predicted.cpu().numpy()

                # Generate classification report
                report = classification_report(y_true, y_pred, output_dict=True)

                # Log final metrics
                mlflow.log_metrics({
                    'accuracy': report['accuracy'],
                    'recall_class_1': report['1']['recall'] if '1' in report else 0,
                    'recall_class_0': report['0']['recall'] if '0' in report else 0,
                    'f1_score_macro': report['macro avg']['f1-score']
                })

                # Log the model
                mlflow.pytorch.log_model(model, "model")

def run_model_experiment(models, experiment_name=None, uri="http://127.0.0.1:5000/"):
    """
    Sets up and runs an MLflow experiment for a dictionary of models.

    Args:
        models (list): A list of tuples, where each tuple contains:
            - model_name (str): The name of the model.
            - params (dict): A dictionary of model parameters.
            - model (object): The instantiated model object.
            - train_set (tuple): A tuple containing training data (X_train, y_train).
            - test_set (tuple): A tuple containing testing data (X_test, y_test).
        experiment_name (str, optional): The name of the MLflow experiment.
            Defaults to "new_exp_{random_value}".
        uri (str, optional): The tracking URI for MLflow.
            Defaults to "http://127.0.0.1:5000/".
    """
    if experiment_name is None:
        experiment_name = f"new_exp_{random.randint(1, 1000)}"

    mlflow.set_experiment(experiment_name)
    mlflow.set_tracking_uri(uri=uri)

    for i, element in enumerate(models):
        model_name = element[0]
        params = element[1]
        model = element[2]
        train_set = element[3]
        test_set = element[4]

        X_train, y_train = train_set
        X_test, y_test = test_set

        model.set_params(**params)
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        report = classification_report(y_test, y_pred, output_dict=True)

        with mlflow.start_run(run_name=model_name):
            mlflow.log_params(params)
            mlflow.log_metrics({
                'accuracy': report['accuracy'],
                'recall_class_1': report['1']['recall'],
                'recall_class_0': report['0']['recall'],
                'f1_score_macro': report['macro avg']['f1-score']
            })

            if "XGB" in model_name:
                mlflow.xgboost.log_model(model, "model")
            elif isinstance(model, (torch.nn.Module)):
                mlflow.pytorch.log_model(model, "model")
            # elif isinstance(model, tf.keras.Model):
            #     mlflow.tensorflow.log_model(model, "model")
            else:
                mlflow.sklearn.log_model(model, "model")




if __name__ == '__main__':
    # Step 1: Create an imbalanced binary classification dataset
    X, y = make_classification(n_samples=1000, n_features=10, n_informative=2, n_redundant=8,
                               weights=[0.9, 0.1], flip_y=0, random_state=42)

    # Split the dataset into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, stratify=y, random_state=42)

    # Convert data to tensors for PyTorch and TensorFlow
    X_train_torch = torch.tensor(X_train, dtype=torch.float32)
    X_test_torch = torch.tensor(X_test, dtype=torch.float32)
    y_train_torch = torch.tensor(y_train, dtype=torch.long)
    y_test_torch = torch.tensor(y_test, dtype=torch.long)

    # X_train_tf = tf.convert_to_tensor(X_train, dtype=tf.float32)
    # X_test_tf = tf.convert_to_tensor(X_test, dtype=tf.float32)
    # y_train_tf = tf.convert_to_tensor(y_train, dtype=tf.int32)
    # y_test_tf = tf.convert_to_tensor(y_test, dtype=tf.int32)

    # Define a simple PyTorch model
    class SimpleNN(nn.Module):
        def __init__(self, input_size, hidden_size, num_classes):
            super(SimpleNN, self).__init__()
            self.fc1 = nn.Linear(input_size, hidden_size)
            self.relu = nn.ReLU()
            self.fc2 = nn.Linear(hidden_size, num_classes)

        def forward(self, x):
            out = self.fc1(x)
            out = self.relu(out)
            out = self.fc2(out)
            return out

    input_size = X_train.shape[1]
    hidden_size = 64
    num_classes = 2
    torch_model = SimpleNN(input_size, hidden_size, num_classes)

    # Define a simple TensorFlow model
    # tf_model = tf.keras.models.Sequential([
    #     tf.keras.layers.Dense(64, activation='relu', input_shape=(X_train.shape[1],)),
    #     tf.keras.layers.Dense(2, activation='softmax')
    # ])

    # Compile the TensorFlow model
    # tf_model.compile(optimizer='adam',
    #               loss='sparse_categorical_crossentropy',
    #               metrics=['accuracy'])

    # # Train the TensorFlow model (for demonstration purposes)
    # tf_model.fit(X_train_tf, y_train_tf, epochs=1)

    models = [
        (
            "Logistic Regression",
            {"C": 1, "solver": 'liblinear'},
            LogisticRegression(),
            (X_train, y_train),
            (X_test, y_test)
        ),
        (
            "Random Forest",
            {"n_estimators": 30, "max_depth": 3},
            RandomForestClassifier(),
            (X_train, y_train),
            (X_test, y_test)
        ),
        (
            "XGBClassifier",
            {"use_label_encoder": False, "eval_metric": 'logloss'},
            XGBClassifier(),
            (X_train, y_train),
            (X_test, y_test)
        ),
        (
            "SimpleNN",
            {"input_size": input_size, "hidden_size": hidden_size, "num_classes": num_classes},
            torch_model,
            (X_train_torch, y_train_torch),
            (X_test_torch, y_test_torch)
        ),
        # (
        #     "TFModel",
        #     {},
        #     tf_model,
        #     (X_train_tf, y_train_tf),
        #     (X_test_tf, y_test_tf)
        # )
    ]

    # Run the general model experiment
    run_model_experiment(models, experiment_name="My Model Experiment")

    # Example of running specific experiments by model type

    # 1. Run sklearn models only
    sklearn_models = [
        (
            "Logistic Regression",
            {"C": 1, "solver": 'liblinear'},
            LogisticRegression(),
            (X_train, y_train),
            (X_test, y_test)
        ),
        (
            "Random Forest",
            {"n_estimators": 30, "max_depth": 3},
            RandomForestClassifier(),
            (X_train, y_train),
            (X_test, y_test)
        )
    ]
    run_sklearn_experiment(sklearn_models, experiment_name="Sklearn Models Only")

    # 2. Run XGBoost models only
    xgb_models = [
        (
            "XGBClassifier",
            {"use_label_encoder": False, "eval_metric": 'logloss'},
            XGBClassifier(),
            (X_train, y_train),
            (X_test, y_test)
        )
    ]
    run_xgboost_experiment(xgb_models, experiment_name="XGBoost Models Only")

    # 3. Run PyTorch models only
    torch_models = [
        (
            "SimpleNN",
            {"input_size": input_size, "hidden_size": hidden_size, "num_classes": num_classes},
            torch_model,
            (X_train_torch, y_train_torch),
            (X_test_torch, y_test_torch)
        )
    ]
    run_torch_experiment(torch_models, experiment_name="PyTorch Models Only", num_epochs=5)