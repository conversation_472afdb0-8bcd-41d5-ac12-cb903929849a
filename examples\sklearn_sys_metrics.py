import mlflow
import mlflow.sklearn
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.datasets import load_diabetes

# Load and split the dataset
data = load_diabetes()
X_train, X_test, y_train, y_test = train_test_split(data.data, data.target, test_size=0.2, random_state=42)

# Start an MLflow experiment
mlflow.set_tracking_uri("https://mlfow-custom-dev.aimlops.itg.ti.com/")
mlflow.set_experiment("Linear Regression Experiment")
mlflow.enable_system_metrics_logging()

mlflow.sklearn.autolog()
with mlflow.start_run():
    model = LinearRegression()
    model.fit(X_train, y_train)
    predictions = model.predict(X_test)
    
    # Log model parameters and metrics
    mlflow.log_param("model_type", "Linear Regression")
    mlflow.log_metric("mse", mean_squared_error(y_test, predictions))
    
    # Log the model itself
    mlflow.sklearn.log_model(model, "linear_regression_model")
    print(f"Model logged with MSE: {mean_squared_error(y_test, predictions)}")
