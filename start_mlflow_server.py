import os
import subprocess
import sys

# Set environment variables for system metrics
os.environ["MLFLOW_ENABLE_SYSTEM_METRICS_LOGGING"] = "true"

# Set stronger cookie restrictions
os.environ["MLFLOW_SERVER_COOKIE_SAMESITE"] = "Strict"
os.environ["MLFLOW_SERVER_SESSION_EXPIRE_SECONDS"] = "0"  # Session expires immediately
os.environ["MLFLOW_FLASK_SERVER_SECRET_KEY"] = os.urandom(24).hex()  # Random key each time
os.environ["MLFLOW_SERVER_COOKIE_SECURE"] = "true"  # Only send over HTTPS
os.environ["MLFLOW_SERVER_COOKIE_HTTPONLY"] = "true"  # Prevent JS access

# Define the command to start MLflow server with authentication
cmd = [
    "mlflow", "server",
    "--host", "0.0.0.0",
    "--port", "5000",
    "--app-name", "basic-auth",
]

# Print the values of environment variables before running the command
if "MLFLOW_TRACKING_USERNAME" in os.environ:
    print(f"MLFLOW_TRACKING_USERNAME: {os.environ['MLFLOW_TRACKING_USERNAME']}")
else :
    print("MLFLOW_TRACKING_USERNAME not set")
if "MLFLOW_TRACKING_PASSWORD" in os.environ:
    print(f"MLFLOW_TRACKING_PASSWORD: {os.environ['MLFLOW_TRACKING_PASSWORD']}")
else:
    print("MLFLOW_TRACKING_PASSWORD not set")

# Print the command
print(f"Starting MLflow server with command: {' '.join(cmd)}")

# Start the MLflow server
try:
    subprocess.run(cmd)
except KeyboardInterrupt:
    print("MLflow server stopped by user")
except Exception as e:
    print(f"Error starting MLflow server: {e}")
    sys.exit(1)
