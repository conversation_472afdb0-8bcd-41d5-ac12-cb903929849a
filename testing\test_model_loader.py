"""
Unit tests for the model_loader module.
"""

import unittest
from unittest.mock import patch, MagicMock
import mlflow
from mlflow.exceptions import MlflowException

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from src.model_loader import (
    load_model, get_model_info, 
    ModelNotFoundError, ModelLoadError
)

class TestModelLoader(unittest.TestCase):
    """Test cases for the model_loader module."""
    
    @patch('mlflow.pyfunc.load_model')
    @patch('mlflow.tracking.MlflowClient')
    def test_load_from_run_id(self, mock_client, mock_load_model):
        """Test loading a model from a run ID."""
        # Setup
        run_id = "test_run_id"
        mock_client.return_value.get_run.return_value = MagicMock()
        mock_load_model.return_value = "mock_model"
        
        # Execute
        model = load_model(run_id=run_id)
        
        # Assert
        mock_client.return_value.get_run.assert_called_once_with(run_id)
        mock_load_model.assert_called_once_with(f"runs:/{run_id}/model")
        self.assertEqual(model, "mock_model")
    
    @patch('mlflow.pyfunc.load_model')
    @patch('mlflow.tracking.MlflowClient')
    def test_load_from_registry_latest(self, mock_client, mock_load_model):
        """Test loading the latest version of a model from the registry."""
        # Setup
        model_name = "test_model"
        mock_version = MagicMock()
        mock_version.version = "1"
        mock_client.return_value.get_latest_versions.return_value = [mock_version]
        mock_load_model.return_value = "mock_model"
        
        # Execute
        model = load_model(model_name=model_name)
        
        # Assert
        mock_client.return_value.get_registered_model.assert_called_once_with(model_name)
        mock_client.return_value.get_latest_versions.assert_called_once_with(model_name)
        mock_load_model.assert_called_once_with(f"models:/{model_name}/1")
        self.assertEqual(model, "mock_model")
    
    @patch('mlflow.pyfunc.load_model')
    @patch('mlflow.tracking.MlflowClient')
    def test_load_from_registry_specific_version(self, mock_client, mock_load_model):
        """Test loading a specific version of a model from the registry."""
        # Setup
        model_name = "test_model"
        version = "2"
        mock_load_model.return_value = "mock_model"
        
        # Execute
        model = load_model(model_name=model_name, model_version=version)
        
        # Assert
        mock_client.return_value.get_registered_model.assert_called_once_with(model_name)
        mock_client.return_value.get_model_version.assert_called_once_with(model_name, version)
        mock_load_model.assert_called_once_with(f"models:/{model_name}/{version}")
        self.assertEqual(model, "mock_model")
    
    @patch('mlflow.pyfunc.load_model')
    @patch('mlflow.tracking.MlflowClient')
    def test_load_from_registry_stage(self, mock_client, mock_load_model):
        """Test loading a model from a specific stage in the registry."""
        # Setup
        model_name = "test_model"
        stage = "Production"
        mock_version = MagicMock()
        mock_client.return_value.get_latest_versions.return_value = [mock_version]
        mock_load_model.return_value = "mock_model"
        
        # Execute
        model = load_model(model_name=model_name, model_stage=stage)
        
        # Assert
        mock_client.return_value.get_registered_model.assert_called_once_with(model_name)
        mock_client.return_value.get_latest_versions.assert_called_once_with(model_name, stages=[stage])
        mock_load_model.assert_called_once_with(f"models:/{model_name}/{stage}")
        self.assertEqual(model, "mock_model")
    
    @patch('mlflow.pyfunc.load_model')
    @patch('mlflow.tracking.MlflowClient')
    def test_load_from_registry_alias(self, mock_client, mock_load_model):
        """Test loading a model with a specific alias from the registry."""
        # Setup
        model_name = "test_model"
        alias = "champion"
        mock_load_model.return_value = "mock_model"
        
        # Execute
        model = load_model(model_name=model_name, model_alias=alias)
        
        # Assert
        mock_client.return_value.get_registered_model.assert_called_once_with(model_name)
        mock_client.return_value.get_model_version_by_alias.assert_called_once_with(model_name, alias)
        mock_load_model.assert_called_once_with(f"models:/{model_name}@{alias}")
        self.assertEqual(model, "mock_model")
    
    @patch('mlflow.tracking.MlflowClient')
    def test_model_not_found_run(self, mock_client):
        """Test handling of a non-existent run."""
        # Setup
        run_id = "non_existent_run"
        mock_client.return_value.get_run.side_effect = MlflowException("RESOURCE_DOES_NOT_EXIST")
        
        # Execute and Assert
        with self.assertRaises(ModelNotFoundError):
            load_model(run_id=run_id)
    
    @patch('mlflow.tracking.MlflowClient')
    def test_model_not_found_registry(self, mock_client):
        """Test handling of a non-existent model in the registry."""
        # Setup
        model_name = "non_existent_model"
        mock_client.return_value.get_registered_model.side_effect = MlflowException("RESOURCE_DOES_NOT_EXIST")
        
        # Execute and Assert
        with self.assertRaises(ModelNotFoundError):
            load_model(model_name=model_name)
    
    def test_invalid_parameters(self):
        """Test handling of invalid parameters."""
        # Execute and Assert
        with self.assertRaises(ValueError):
            load_model(model_name="test", model_version="1", model_stage="Production")
    
    def test_missing_parameters(self):
        """Test handling of missing parameters."""
        # Execute and Assert
        with self.assertRaises(ValueError):
            load_model()
    
    @patch('mlflow.pyfunc.load_model')
    def test_direct_uri(self, mock_load_model):
        """Test loading a model with a direct URI."""
        # Setup
        uri = "runs:/test_run/model"
        mock_load_model.return_value = "mock_model"
        
        # Execute
        model = load_model(model_identifier=uri)
        
        # Assert
        mock_load_model.assert_called_once_with(uri)
        self.assertEqual(model, "mock_model")

if __name__ == '__main__':
    unittest.main()
