import mlflow
import mlflow.sklearn
import mlflow.xgboost

import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.metrics import classification_report
import warnings
warnings.filterwarnings('ignore')

# Step 1: Create an imbalanced binary classification dataset
X, y = make_classification(n_samples=1000, n_features=10, n_informative=2, n_redundant=8, 
                           weights=[0.9, 0.1], flip_y=0, random_state=42)

np.unique(y, return_counts=True)

# Split the dataset into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, stratify=y, random_state=42)

# Define the model hyperparameters
params = {
    "solver": "lbfgs",
    "max_iter": 1000,
    "multi_class": "auto",
    "random_state": 8888,
}

# Train the model
lr = LogisticRegression(**params)
lr.fit(X_train, y_train)

# Predict on the test set
y_pred = lr.predict(X_test)

report = classification_report(y_test, y_pred)
print(report)

report_dict = classification_report(y_test, y_pred, output_dict=True)
report_dict

mlflow.set_experiment("First Experiment")
mlflow.set_tracking_uri(uri="http://127.0.0.1:5000/")

with mlflow.start_run():
    mlflow.log_params(params)
    mlflow.log_metrics({
        'accuracy': report_dict['accuracy'],
        'recall_class_0': report_dict['0']['recall'],
        'recall_class_1': report_dict['1']['recall'],
        'f1_score_macro': report_dict['macro avg']['f1-score']
    })
    mlflow.sklearn.log_model(lr, "Logistic Regression")  

models = [
    (
        "Logistic Regression", 
        {"C": 1, "solver": 'liblinear'},
        LogisticRegression(), 
        (X_train, y_train),
        (X_test, y_test)
    ),
    (
        "Random Forest", 
        {"n_estimators": 30, "max_depth": 3},
        RandomForestClassifier(), 
        (X_train, y_train),
        (X_test, y_test)
    ),
    (
        "XGBClassifier",
        {"use_label_encoder": False, "eval_metric": 'logloss'},
        XGBClassifier(), 
        (X_train, y_train),
        (X_test, y_test)
    ),
]

reports = []

for model_name, params, model, train_set, test_set in models:
    X_train = train_set[0]
    y_train = train_set[1]
    X_test = test_set[0]
    y_test = test_set[1]
    
    model.set_params(**params)
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    report = classification_report(y_test, y_pred, output_dict=True)
    reports.append(report)

reports

mlflow.set_experiment("Anomaly Detection")
mlflow.set_tracking_uri(uri="http://127.0.0.1:5000/")

for i, element in enumerate(models):
    model_name = element[0]
    params = element[1]
    model = element[2]
    report = reports[i]
    
    with mlflow.start_run(run_name=model_name):        
        mlflow.log_params(params)
        mlflow.log_metrics({
            'accuracy': report['accuracy'],
            'recall_class_1': report['1']['recall'],
            'recall_class_0': report['0']['recall'],
            'f1_score_macro': report['macro avg']['f1-score']
        })  
        
        if "XGB" in model_name:
            mlflow.xgboost.log_model(model, "model")
        else:
            mlflow.sklearn.log_model(model, "model")
 

model_name = 'Random Forest'
run_id=input('Please type RunID')
model_uri = f'runs:/{run_id}/model_name'

from datetime import datetime
timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

tags = {
    "model_type": "Random Forest Classifier",
    "created_by": "MLFlow Pipeline",
    "timestamp": timestamp,
    "data_version": "v1.0",
    "purpose": "Anomaly Detection"
}

description = f"""
Random Forest Model for Anomaly Detection
Created on: {timestamp}
Parameters: {models[1][1]}

Model trained on imbalanced binary classification dataset with 1000 samples and 10 features.
"""


# with mlflow.start_run(run_id=run_id):
#     mlflow.register_model(model_uri=model_uri, name=model_name, tags=tags)
    

model_version = "1"

client = mlflow.MlflowClient()
# Update Description
client.update_model_version(
    name=model_name,
    version=model_version,
    description=description,
)

# Add/Update Tags
for key, value in tags.items():
    client.set_model_version_tag(
        name=model_name, version=model_version, key=key, value=value
    )

# client.set_registered_model_tag(
#     name=model_name, key="framework", value="scikit-learn"
# )

# client.delete_model_version(name=model_name, version=model_version)



model_version = 1
# model_name 
model_uri = f"models:/{model_name}/{model_version}"

loaded_model = mlflow.sklearn.load_model(model_uri)
# y_pred = loaded_model.predict(models[1][4][0])
# y_pred[:4]

import mlflow
from mlflow.tracking import MlflowClient
from datetime import datetime

def register_model_with_metadata(model_name, run_id, tags=None, description=None, alias=None, created_by=None):
    """
    Registers an MLflow model and updates it with optional tags, description, and alias.

    Args:
        model_name (str): The name to assign to the registered model.
        run_id (str): The MLflow run ID associated with the model.
        tags (dict, optional): A dictionary of tags to add to the registered model. Defaults to None.
        description (str, optional): A description for the registered model. Defaults to None.
        alias (str, optional): An alias for the model. Defaults to None.
        created_by (str, optional): The creator of the model. Defaults to None.
    """

    model_uri = f'runs:/{run_id}/model'
    client = MlflowClient()

    try:
        # Register the model
        registered_model_version = mlflow.register_model(
            model_uri=model_uri,
            name=model_name,
            tags=tags if tags else {}
        )

        # Update the description (if provided)
        if description:
            client.update_model_version(
                name=model_name,
                version=registered_model_version.version,
                description=description
            )

        # Add alias tag (if provided)
        if alias:
            client.set_registered_model_alias(
            name=model_name,
            alias=alias,
            version=registered_model_version.version
            )

        # Add created_by tag (if provided)
        if created_by:
             client.set_model_version_tag(
                name=model_name,
                version=registered_model_version.version,
                key="created_by",
                value=created_by
            )

        print(f"Model '{model_name}' registered successfully with run_id '{run_id}'.")

    except Exception as e:
        print(f"Error registering model: {e}")


if __name__ == '__main__':
    model_name = 'Random Forest'
    run_id = input('Please type RunID: ')

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    default_tags = {
        "model_type": "Random Forest Classifier",
        "timestamp": timestamp,
        "data_version": "v1.0",
        "purpose": "Anomaly Detection"
    }

    default_description = f"""
    Random Forest Model for Anomaly Detection
    Created on: {timestamp}
    Model trained on imbalanced binary classification dataset with 1000 samples and 10 features.
    """

    register_model_with_metadata(
        model_name=model_name,
        run_id=run_id,
        tags=default_tags,
        description=default_description,
        alias="initial_model",
        created_by="Lucifer473"
    )

import torch
import torch.nn as nn
import torch.optim as optim
from src.mlflow_pytorch_utils import log_pytorch_model

class SimpleNN(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(SimpleNN, self).__init__()
        self.layer1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.layer2 = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        x = self.layer1(x)
        x = self.relu(x)
        x = self.layer2(x)
        return x

torch.manual_seed(42)
    
# Define model parameters
input_size = 10
hidden_size = 20
output_size = 1
learning_rate = 0.01
num_epochs = 100

# Create model parameters dictionary
model_params = {
    'input_size': input_size,
    'hidden_size': hidden_size,
    'output_size': output_size,
    'learning_rate': learning_rate,
    'num_epochs': num_epochs,
    'optimizer': 'Adam'
}

# Create model
model = SimpleNN(input_size, hidden_size, output_size)

# Create some dummy data for training
X = torch.randn(100, input_size)
y = torch.randn(100, output_size)

# Define loss function and optimizer
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=learning_rate)

# Train the model
for epoch in range(num_epochs):
    # Forward pass
    outputs = model(X)
    loss = criterion(outputs, y)
    
    # Backward and optimize
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    # Print progress
    if (epoch+1) % 10 == 0:
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')

# Create input example for model signature
input_example = torch.randn(1, input_size)

# Set up MLflow experiment
experiment_name = "PyTorch_Model_Example"

# Log the model to MLflow
run_id = log_pytorch_model(
    model=model,
    model_params=model_params,
    experiment_name=experiment_name,
    run_name="simple_nn_run",
    input_example=input_example,
    tags={"model_type": "regression", "framework": "pytorch"}
)

print(f"Model logged to MLflow with run_id: {run_id}")
    

import mlflow
from src.mlflow_pytorch_utils import log_pytorch_model
from src.register_update import register_model_with_metadata
from src.mlflow_sklearn_utils import log_sklearn_model
from src.mlflow_xgboost_utils import log_xgboost_model
from src.model_loader import load_model,get_model_info
from datetime import datetime

mlflow.set_tracking_uri("http://127.0.0.1:5000/")

model_name = "simple torch"
run_id= "e4517ea908d64a66894ceef991c2dc49"
timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
default_tags = {
    "model_type": "PyTorch Model",
    "timestamp": timestamp,
    "data_version": "v1.0",
    "purpose": "Sample testing"
}

default_description = f"""
    PyTorch Model for testing
    Created on: {timestamp}
    Model trained on random data.
"""

register_model_with_metadata(
    model_name=model_name,
    run_id=run_id,
    version=2,
    tags=default_tags,
    description=default_description,
    alias="initial_torch_model",
    created_by="Lucifer473"
)

model = load_model(run_id= "0f21a2438da94e02864592ee8dd7ea13")


model

model = load_model(model_name="simple torch", model_version=1)

model



# info =get_model_info(model_name ="e4517ea908d64a66894ceef991c2dc49")
# info

# info =get_model_info(model_name ="simple torch")
# info

!export https_proxy=webproxy.ext.ti.com:80

import mlflow
import mlflow.sklearn
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.datasets import load_diabetes

# Load and split the dataset
data = load_diabetes()
X_train, X_test, y_train, y_test = train_test_split(data.data, data.target, test_size=0.2, random_state=42)

# Start an MLflow experiment
mlflow.set_tracking_uri("http://127.0.0.1:5000/")
mlflow.set_experiment("Linear Regression Experiment")
mlflow.enable_system_metrics_logging()

mlflow.sklearn.autolog()
with mlflow.start_run():
    model = LinearRegression()
    model.fit(X_train, y_train)
    predictions = model.predict(X_test)
    
    # Log model parameters and metrics
    mlflow.log_param("model_type", "Linear Regression")
    mlflow.log_metric("mse", mean_squared_error(y_test, predictions))
    
    # Log the model itself
    mlflow.sklearn.log_model(model, "linear_regression_model")
    print(f"Model logged with MSE: {mean_squared_error(y_test, predictions)}")

import mlflow
from mlflow.models.signature import ModelSignature
from mlflow.types import DataType, Schema, ColSpec

# Set tracking URI to localhost port 5000
mlflow.set_tracking_uri("http://127.0.0.1:5000/")
print(f"MLflow Tracking URI: {mlflow.get_tracking_uri()}")

# Create experiment for prompts
mlflow.set_experiment("Summarization_Prompts")

# Define input/output schema for the prompt
input_schema = Schema([ColSpec(DataType.STRING, "text")])
output_schema = Schema([ColSpec(DataType.STRING, "summary")])
signature = ModelSignature(inputs=input_schema, outputs=output_schema)

# Version 1.0 - Initial summarization prompt
prompt_v1 = """
Summarize the following text in a concise manner:
{{text}}
"""

# Log the first version of the prompt
with mlflow.start_run(run_name="summarization_prompt_v1"):
    mlflow.log_param("version", "1.0")
    mlflow.log_param("type", "summarization")
    
    # Log the prompt as a model
    mlflow.models.Model.log(
        artifact_path="prompt",
        flavor=mlflow.pyfunc,
        prompt=prompt_v1,
        signature=signature,
        registered_model_name="SummarizationPrompt"
    )
    print("Prompt v1.0 logged successfully")

# Version 2.0 - Updated summarization prompt
prompt_v2 = """
Provide a comprehensive summary of the following text, highlighting key points:
{{text}}
"""

# Log the second version of the prompt
with mlflow.start_run(run_name="summarization_prompt_v2"):
    mlflow.log_param("version", "2.0")
    mlflow.log_param("type", "summarization")
    
    # Log the prompt as a model
    mlflow.models.Model.log(
        artifact_path="prompt",
        flavor=mlflow.pyfunc,
        prompt=prompt_v2,
        signature=signature,
        registered_model_name="SummarizationPrompt"
    )
    print("Prompt v2.0 logged successfully")

# Version 2.1 - Slightly modified version of v2.0
prompt_v2_1 = """
Provide a comprehensive summary of the following text, highlighting key points and main ideas:
{{text}}
"""

# Log the version 2.1 of the prompt
with mlflow.start_run(run_name="summarization_prompt_v2_1"):
    mlflow.log_param("version", "2.1")
    mlflow.log_param("type", "summarization")
    
    # Log the prompt as a model
    mlflow.models.Model.log(
        artifact_path="prompt",
        flavor=mlflow.pyfunc,
        prompt=prompt_v2_1,
        signature=signature,
        registered_model_name="SummarizationPrompt"
    )
    print("Prompt v2.1 logged successfully")

# Get the latest version of the prompt
client = mlflow.tracking.MlflowClient()
latest_version = client.get_latest_versions("SummarizationPrompt", stages=["None"])[0].version
print(f"Latest version of SummarizationPrompt: {latest_version}")

# You can also retrieve a specific version
model_version_details = client.get_model_version("SummarizationPrompt", "1")
print(f"Details of version 1: {model_version_details.name} (version: {model_version_details.version})")

import os
from mlflow import MlflowClient
from mlflow.server import get_app_client

# Set admin credentials as environment variables
os.environ["MLFLOW_TRACKING_USERNAME"] = "admin"
os.environ["MLFLOW_TRACKING_PASSWORD"] = "password1234"

tracking_uri = "http://localhost:5000/"
client = MlflowClient(tracking_uri=tracking_uri)
# experiment_id = client.create_experiment(name="experiment")

auth_client = get_app_client("basic-auth", tracking_uri=tracking_uri)
auth_client.create_user(username="user1", password="randompassword1")
auth_client.create_user(username="user2", password="randompassword2")

import sqlite3  # Or the appropriate database library


def list_users(db_path):
    """Lists all users from the database."""
    conn = sqlite3.connect(db_path)  # Replace with your database connection
    cursor = conn.cursor()
    cursor.execute("SELECT id, username, password_hash, is_admin FROM users")  # Adjust the query
    users = cursor.fetchall()
    conn.close()
    return [
        {"id": user[0], "username": user[1], "password": user[2], "is_admin": user[3]}
        for user in users
    ]

# Example usage (assuming SQLite):
db_file = "basic_auth.db"  # Replace with the actual path to your MLflow database
users = list_users(db_file)
for user in users:
    print(
        f"ID: {user['id']}, Username: {user['username']}, Password: {user['password']}, Admin: {user['is_admin']}"
    )

import requests
import json
import os

# Define the API endpoint and credentials
endpoint = "http://localhost:5000/api/2.0/mlflow/users/update-password"
username = "user1"
new_password = "randompassword12"

# Set admin credentials as environment variables
os.environ["MLFLOW_TRACKING_USERNAME"] = "admin"
os.environ["MLFLOW_TRACKING_PASSWORD"] = "password1234"

# Create a dictionary with the request data
data = {
    "username": username,
    "password": new_password
}

# Create a new session object
session = requests.Session()

# Add headers to prevent caching
headers = {
    "Cache-Control": "no-cache, no-store, must-revalidate",
    "Pragma": "no-cache",
    "Expires": "0"
}

# Send the PATCH request with authentication and headers
response = session.patch(
    endpoint, 
    json=data, 
    headers=headers,
    auth=(os.environ["MLFLOW_TRACKING_USERNAME"], os.environ["MLFLOW_TRACKING_PASSWORD"])
)

# Check if the request was successful
if response.status_code == 200:
    print("Password updated successfully")
else:
    print("Error updating password:", response.text)

# Clear the session cookies
session.cookies.clear()

# Close the session
session.close()

import requests
import json
from abc import ABC, abstractmethod

class MLflowAPI(ABC):
    def __init__(self, base_url="http://localhost:5000", username="admin", password="password1234"):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.session = requests.Session()

    @abstractmethod
    def create_user(self, username, password):
        """
        Create a new user.

        :param username: The username of the new user.
        :param password: The password of the new user.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/users/create"
        data = {"username": username, "password": password}
        response = self.session.post(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def get_user(self, username):
        """
        Get a user by username.

        :param username: The username of the user to get.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/users/get"
        params = {"username": username}
        response = self.session.get(endpoint, params=params, auth=(self.username, self.password))
        return response

    @abstractmethod
    def update_user_password(self, username, new_password):
        """
        Update a user's password.

        :param username: The username of the user to update.
        :param new_password: The new password for the user.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/users/update-password"
        data = {"username": username, "password": new_password}
        response = self.session.patch(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def update_user_admin(self, username, is_admin):
        """
        Update a user's admin status.

        :param username: The username of the user to update.
        :param is_admin: Whether the user should be an admin.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/users/update-admin"
        data = {"username": username, "is_admin": is_admin}
        response = self.session.patch(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def delete_user(self, username):
        """
        Delete a user.

        :param username: The username of the user to delete.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/users/delete"
        params = {"username": username}
        response = self.session.delete(endpoint, params=params, auth=(self.username, self.password))
        return response

    @abstractmethod
    def create_experiment_permission(self, experiment_id, username, permission):
        """
        Create a new experiment permission.

        :param experiment_id: The ID of the experiment.
        :param username: The username of the user to grant permission to.
        :param permission: The permission to grant.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/experiments/permissions/create"
        data = {"experiment_id": experiment_id, "username": username, "permission": permission}
        response = self.session.post(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def get_experiment_permission(self, experiment_id, username):
        """
        Get an experiment permission.

        :param experiment_id: The ID of the experiment.
        :param username: The username of the user to get permission for.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/experiments/permissions/get"
        params = {"experiment_id": experiment_id, "username": username}
        response = self.session.get(endpoint, params=params, auth=(self.username, self.password))
        return response

    @abstractmethod
    def update_experiment_permission(self, experiment_id, username, permission):
        """
        Update an experiment permission.

        :param experiment_id: The ID of the experiment.
        :param username: The username of the user to update permission for.
        :param permission: The new permission to grant.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/experiments/permissions/update"
        data = {"experiment_id": experiment_id, "username": username, "permission": permission}
        response = self.session.patch(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def delete_experiment_permission(self, experiment_id, username):
        """
        Delete an experiment permission.

        :param experiment_id: The ID of the experiment.
        :param username: The username of the user to delete permission for.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/experiments/permissions/delete"
        params = {"experiment_id": experiment_id, "username": username}
        response = self.session.delete(endpoint, params=params, auth=(self.username, self.password))
        return response

    @abstractmethod
    def create_registered_model_permission(self, name, username, permission):
        """
        Create a new registered model permission.

        :param name: The name of the registered model.
        :param username: The username of the user to grant permission to.
        :param permission: The permission to grant.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/registered-models/permissions/create"
        data = {"name": name, "username": username, "permission": permission}
        response = self.session.post(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def get_registered_model_permission(self, name, username):
        """
        Get a registered model permission.

        :param name: The name of the registered model.
        :param username: The username of the user to get permission for.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/registered-models/permissions/get"
        params = {"name": name, "username": username}
        response = self.session.get(endpoint, params=params, auth=(self.username, self.password))
        return response

    @abstractmethod
    def update_registered_model_permission(self, name, username, permission):
        """
        Update a registered model permission.

        :param name: The name of the registered model.
        :param username: The username of the user to update permission for.
        :param permission: The new permission to grant.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/registered-models/permissions/update"
        data = {"name": name, "username": username, "permission": permission}
        response = self.session.patch(endpoint, json=data, auth=(self.username, self.password))
        return response

    @abstractmethod
    def delete_registered_model_permission(self, name, username):
        """
        Delete a registered model permission.

        :param name: The name of the registered model.
        :param username: The username of the user to delete permission for.
        :return: The response from the API.
        """
        endpoint = f"{self.base_url}/api/2.0/mlflow/registered-models/permissions/delete"
        params = {"name": name, "username": username}
        response = self.session.delete(endpoint, params=params, auth=(self.username, self.password))
        return response

class MLflowAPIImpl(MLflowAPI):
    def create_user(self, username, password):
        return super().create_user(username, password)

    def get_user(self, username):
        return super().get_user(username)

    def update_user_password(self, username, new_password):
        return super().update_user_password(username, new_password)

    def update_user_admin(self, username, is_admin):
        return super().update_user_admin(username, is_admin)

    def delete_user(self, username):
        return super().delete_user(username)

    def create_experiment_permission(self, experiment_id, username, permission):
        return super().create_experiment_permission(experiment_id, username, permission)

    def get_experiment_permission(self, experiment_id, username):
        return super().get_experiment_permission(experiment_id, username)

    def update_experiment_permission(self, experiment_id, username, permission):
        return super().update_experiment_permission(experiment_id, username, permission)

    def delete_experiment_permission(self, experiment_id, username):
        return super().delete_experiment_permission(experiment_id, username)

    def create_registered_model_permission(self, name, username, permission):
        return super().create_registered_model_permission(name, username, permission)

    def get_registered_model_permission(self, name, username):
        return super().get_registered_model_permission(name, username)

    def update_registered_model_permission(self, name, username, permission):
        return super().update_registered_model_permission(name, username, permission)

    def delete_registered_model_permission(self, name, username):
        return super().delete_registered_model_permission(name, username)

# Usage example
mlflow_api = MLflowAPIImpl()
response = mlflow_api.create_user("new_user", "new_password")
print(response.text)

# Clear cookies
mlflow_api.session.cookies.clear()

#close session
mlflow_api.session.close()



import mlflow
import requests
import time

# Set tracking URI
tracking_uri = "https://mlfow-custom-dev.aimlops.itg.ti.com/"
print(f"Testing connection to: {tracking_uri}")

# Test basic connectivity first
try:
    start = time.time()
    response = requests.get(tracking_uri, timeout=10,verify=False)
    print(f"Server response: {response.status_code}")
    print(f"Response time: {time.time() - start:.2f}s")
except Exception as e:
    print(f"Connection error: {e}")

# Try setting tracking URI with timeout
try:
    mlflow.set_tracking_uri(tracking_uri)
    print(f"Successfully set tracking URI to: {mlflow.get_tracking_uri()}")
except Exception as e:
    print(f"Error setting tracking URI: {e}")

# Try listing experiments (with timeout protection)
try:
    print("Attempting to list experiments (10s timeout)...")
    start = time.time()
    # Use a separate thread or process to prevent hanging
    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(mlflow.list_experiments)
        experiments = future.result(timeout=10)
    print(f"Found {len(experiments)} experiments in {time.time() - start:.2f}s")
except concurrent.futures.TimeoutError:
    print("Operation timed out - server not responding")
except Exception as e:
    print(f"Error listing experiments: {e}")

import mlflow

mlflow.set_tracking_uri("http://127.0.0.1:5000/")
mlflow.set_experiment("Trace Test")

@mlflow.trace(span_type="func", attributes={"key": "value"})
def add_1(x):
    return x + 1

@mlflow.trace(span_type="func", attributes={"key1": "value1"})
def minus_1(x):
    return x - 1

@mlflow.trace(name="Trace Test")
def trace_test(x):
    step1 = add_1(x)
    return minus_1(step1)

trace_test(4)

import mlflow
import mlflow.sklearn
import os
import time
import warnings
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.datasets import load_diabetes
import requests

# Disable SSL verification warnings
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# Set environment variables to disable SSL verification
os.environ['MLFLOW_TRACKING_INSECURE_TLS'] = 'true'

# Load and split the dataset
data = load_diabetes()
X_train, X_test, y_train, y_test = train_test_split(data.data, data.target, test_size=0.2, random_state=42)

# Set tracking URI
tracking_uri = "https://mlfow-custom-dev.aimlops.itg.ti.com/"
print(f"Testing connection to: {tracking_uri}")

# Test basic connectivity first
try:
    start = time.time()
    response = requests.get(tracking_uri, timeout=10, verify=False)
    print(f"Server response: {response.status_code}")
    print(f"Response time: {time.time() - start:.2f}s")
except Exception as e:
    print(f"Connection error: {e}")

# Try setting tracking URI with timeout
try:
    mlflow.set_tracking_uri(tracking_uri)
    print(f"Successfully set tracking URI to: {mlflow.get_tracking_uri()}")
except Exception as e:
    print(f"Error setting tracking URI: {e}")

# Start an MLflow experiment
mlflow.set_experiment("Linear Regression Experiment")
mlflow.enable_system_metrics_logging()

# Remove autolog
# mlflow.sklearn.autolog()
with mlflow.start_run():
    # Train the model
    model = LinearRegression()
    model.fit(X_train, y_train)
    predictions = model.predict(X_test)
    mse = mean_squared_error(y_test, predictions)
    
    # Log model parameters manually
    mlflow.log_param("model_type", "Linear Regression")
    mlflow.log_param("fit_intercept", model.fit_intercept)
    mlflow.log_param("normalize", getattr(model, "normalize", False))
    mlflow.log_param("n_features", X_train.shape[1])
    
    # Log model coefficients
    for i, coef in enumerate(model.coef_):
        mlflow.log_param(f"coef_{i}", coef)
    mlflow.log_param("intercept", model.intercept_)
    
    # Log metrics manually
    mlflow.log_metric("mse", mse)
    mlflow.log_metric("rmse", mse ** 0.5)
    mlflow.log_metric("r2_score", model.score(X_test, y_test))
    
    # Log the model itself
    mlflow.sklearn.log_model(model, "linear_regression_model")
    
    # Log sample predictions as artifacts
    import numpy as np
    import pandas as pd
    
    # Create a sample predictions dataframe
    sample_indices = np.random.choice(len(X_test), min(5, len(X_test)), replace=False)
    sample_predictions = pd.DataFrame({
        "actual": y_test[sample_indices],
        "predicted": predictions[sample_indices],
        "difference": y_test[sample_indices] - predictions[sample_indices]
    })
    
    # Save and log the predictions
    sample_predictions_path = "sample_predictions.csv"
    sample_predictions.to_csv(sample_predictions_path, index=False)
    mlflow.log_artifact(sample_predictions_path)
    
    print(f"Model logged with MSE: {mse}")

import mlflow
import mlflow.sklearn
import os
import time
import warnings
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.datasets import load_diabetes
import requests

# Disable SSL verification warnings
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# Set environment variables to disable SSL verification
os.environ['MLFLOW_TRACKING_INSECURE_TLS'] = 'true'

# Load and split the dataset
data = load_diabetes()
X_train, X_test, y_train, y_test = train_test_split(data.data, data.target, test_size=0.2, random_state=42)

# Set tracking URI
tracking_uri = "http://lelvdckdvsset.itg.ti.com:5000/"
print(f"Testing connection to: {tracking_uri}")

# Test basic connectivity first
try:
    start = time.time()
    response = requests.get(tracking_uri, timeout=10, verify=False)
    print(f"Server response: {response.status_code}")
    print(f"Response time: {time.time() - start:.2f}s")
except Exception as e:
    print(f"Connection error: {e}")

# Try setting tracking URI with timeout
try:
    mlflow.set_tracking_uri(tracking_uri)
    print(f"Successfully set tracking URI to: {mlflow.get_tracking_uri()}")
except Exception as e:
    print(f"Error setting tracking URI: {e}")

os.environ['MLFLOW_TRACKING_USERNAME'] = 'admin'
os.environ['MLFLOW_TRACKING_PASSWORD'] = 'password1234'

# Start an MLflow experiment
mlflow.set_experiment("Linear reg")
mlflow.enable_system_metrics_logging()

# Remove autolog
# mlflow.sklearn.autolog()
with mlflow.start_run():
    # Train the model
    model = LinearRegression()
    model.fit(X_train, y_train)
    predictions = model.predict(X_test)
    mse = mean_squared_error(y_test, predictions)
    
    # Log model parameters manually
    mlflow.log_param("model_type", "Linear Regression")
    mlflow.log_param("fit_intercept", model.fit_intercept)
    mlflow.log_param("normalize", getattr(model, "normalize", False))
    mlflow.log_param("n_features", X_train.shape[1])
    
    # Log model coefficients
    for i, coef in enumerate(model.coef_):
        mlflow.log_param(f"coef_{i}", coef)
    mlflow.log_param("intercept", model.intercept_)
    
    # Log metrics manually
    mlflow.log_metric("mse", mse)
    mlflow.log_metric("rmse", mse ** 0.5)
    mlflow.log_metric("r2_score", model.score(X_test, y_test))
    
    # Log the model itself
    # mlflow.sklearn.log_model(model, "linear_regression_model")
    
    # Log sample predictions as artifacts
    import numpy as np
    import pandas as pd
    
    # Create a sample predictions dataframe
    sample_indices = np.random.choice(len(X_test), min(5, len(X_test)), replace=False)
    sample_predictions = pd.DataFrame({
        "actual": y_test[sample_indices],
        "predicted": predictions[sample_indices],
        "difference": y_test[sample_indices] - predictions[sample_indices]
    })
    
    # Save and log the predictions
    sample_predictions_path = "sample_predictions.csv"
    sample_predictions.to_csv(sample_predictions_path, index=False)
    mlflow.log_artifact(sample_predictions_path)
    
    print(f"Model logged with MSE: {mse}")

