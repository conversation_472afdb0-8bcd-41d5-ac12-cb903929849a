# Stage 1: Build
FROM ghcr.io/mlflow/mlflow:v2.22.0 AS build

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="${PYTHONPATH}:/app" \
    TZ=UTC

# ENVs for proxy setup in order to download dependencies
ENV HTTP_PROXY=http://webproxy.ext.ti.com:80
ENV HTTPS_PROXY=http://webproxy.ext.ti.com:80
# Set the working directory in the container
WORKDIR /app

# Create a non-root user
ENV APP_USER=appuser
ENV APP_HOME=/app
RUN groupadd -r $APP_USER && \
    useradd -r -g $APP_USER -d $APP_HOME -s /sbin/nologin -c "Docker image user" $APP_USER

# Install Python dependencies (CPU-only PyTorch for reduced size)
RUN pip install --no-cache-dir torch --index-url https://download.pytorch.org/whl/cpu && \
    pip install --no-cache-dir xgboost && \
    # Clean pip cache to reduce image size
    rm -rf /root/.cache/pip

# Stage 2: Run - Use a slim base image for the final stage
FROM ghcr.io/mlflow/mlflow:v2.22.0 AS final

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="${PYTHONPATH}:/app" \
    TZ=UTC \
    APP_USER=appuser \
    APP_HOME=/app \
    HTTP_PROXY=http://webproxy.ext.ti.com:80 \
    HTTPS_PROXY=http://webproxy.ext.ti.com:80

# Set the working directory in the container
WORKDIR /app

# Create a non-root user
RUN groupadd -r $APP_USER && \
    useradd -r -g $APP_USER -d $APP_HOME -s /sbin/nologin -c "Docker image user" $APP_USER

# Install only the required Python packages from the build stage
# Copy PyTorch CPU-only and its dependencies
COPY --from=build /usr/local/lib/python3.*/site-packages/torch/ /usr/local/lib/python3.*/site-packages/torch/
COPY --from=build /usr/local/lib/python3.*/site-packages/torch-*.dist-info/ /usr/local/lib/python3.*/site-packages/
COPY --from=build /usr/local/lib/python3.*/site-packages/typing_extensions* /usr/local/lib/python3.*/site-packages/
COPY --from=build /usr/local/lib/python3.*/site-packages/filelock* /usr/local/lib/python3.*/site-packages/
COPY --from=build /usr/local/lib/python3.*/site-packages/sympy/ /usr/local/lib/python3.*/site-packages/sympy/
COPY --from=build /usr/local/lib/python3.*/site-packages/sympy-*.dist-info/ /usr/local/lib/python3.*/site-packages/
COPY --from=build /usr/local/lib/python3.*/site-packages/mpmath/ /usr/local/lib/python3.*/site-packages/mpmath/
COPY --from=build /usr/local/lib/python3.*/site-packages/mpmath-*.dist-info/ /usr/local/lib/python3.*/site-packages/

# Copy XGBoost and its dependencies
COPY --from=build /usr/local/lib/python3.*/site-packages/xgboost/ /usr/local/lib/python3.*/site-packages/xgboost/
COPY --from=build /usr/local/lib/python3.*/site-packages/xgboost-*.dist-info/ /usr/local/lib/python3.*/site-packages/
COPY --from=build /usr/local/lib/python3.*/site-packages/numpy/ /usr/local/lib/python3.*/site-packages/numpy/
COPY --from=build /usr/local/lib/python3.*/site-packages/numpy-*.dist-info/ /usr/local/lib/python3.*/site-packages/
COPY --from=build /usr/local/lib/python3.*/site-packages/scipy/ /usr/local/lib/python3.*/site-packages/scipy/
COPY --from=build /usr/local/lib/python3.*/site-packages/scipy-*.dist-info/ /usr/local/lib/python3.*/site-packages/

# Copy application code
COPY . /app/

# Create directory for MLflow artifacts with proper permissions
RUN mkdir -p /app/mlruns && \
    chmod -R 777 /app/mlruns && \
    chown -R $APP_USER:$APP_USER $APP_HOME && \
    pip install --no-cache-dir mlflow[auth] 

# Specify the user (should be a non-root user)
USER $APP_USER

# Expose the port MLflow runs on
EXPOSE 5000

# Unset proxy variables
ENV HTTP_PROXY=
ENV HTTPS_PROXY=

# Use start_mlflow_server.py instead of direct MLflow command
CMD ["python", "start_mlflow_server.py"]
