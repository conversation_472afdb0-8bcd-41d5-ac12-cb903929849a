import sys
import os
import time
import mlflow

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set environment variable to enable system metrics
os.environ["MLFLOW_ENABLE_SYSTEM_METRICS_LOGGING"] = "true"

# Set tracking URI
mlflow.set_tracking_uri("http://localhost:5000")
print(f"MLflow tracking URI: {mlflow.get_tracking_uri()}")

# Set experiment
experiment_name = "Sklearn Sys Metrics Test"
mlflow.set_experiment(experiment_name)

# Enable system metrics logging
mlflow.enable_system_metrics_logging()

# Start a run with system metrics enabled
with mlflow.start_run(run_name="System_Metrics_Test_Run") as run:
    # Log some parameters
    mlflow.log_param("test_param", "test_value")

    # Perform some CPU-intensive operations to generate metrics
    print("Performing CPU-intensive operations...")
    for i in range(10):
        # Simulate CPU load
        start_time = time.time()
        while time.time() - start_time < 1:
            _ = [i**2 for i in range(10000)]

        # Log a metric
        mlflow.log_metric("iteration", i)
        print(f"Completed iteration {i}")

    print(f"Run ID: {run.info.run_id}")
    print(f"Experiment ID: {run.info.experiment_id}")
    print("Check the MLflow UI to see if system metrics are being displayed")
