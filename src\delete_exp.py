import mlflow
import argpar<PERSON>
def delete_experiment(experiment_name):
    """
    Deletes an experiment and all its associated runs from MLflow.

    Args:
        experiment_name (str): The name of the experiment to delete.
    """
    # Get the experiment ID
    experiment = mlflow.get_experiment_by_name(experiment_name)
    if experiment is None:
        print(f"Experiment '{experiment_name}' does not exist.")
        return

    experiment_id = experiment.experiment_id

    # Delete the experiment and all its runs
    mlflow.delete_experiment(experiment_id)
    print(f"Experiment '{experiment_name}' deleted.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Delete an MLflow experiment.")
    parser.add_argument("experiment_name", type=str, help="The name of the experiment to delete.")
    args = parser.parse_args()

    delete_experiment(args.experiment_name)