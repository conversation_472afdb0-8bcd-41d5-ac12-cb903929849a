import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import mlflow
from src.mlflow_pytorch_utils import log_pytorch_model


# Define a simple PyTorch model
class SimpleNN(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(SimpleNN, self).__init__()
        self.layer1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.layer2 = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        x = self.layer1(x)
        x = self.relu(x)
        x = self.layer2(x)
        return x


def main():
    # Set random seed for reproducibility
    torch.manual_seed(42)
    
    # Define model parameters
    input_size = 10
    hidden_size = 20
    output_size = 1
    learning_rate = 0.01
    num_epochs = 100
    
    # Create model parameters dictionary
    model_params = {
        'input_size': input_size,
        'hidden_size': hidden_size,
        'output_size': output_size,
        'learning_rate': learning_rate,
        'num_epochs': num_epochs,
        'optimizer': 'Adam'
    }
    
    # Create model
    model = SimpleNN(input_size, hidden_size, output_size)
    
    # Create some dummy data for training
    X = torch.randn(100, input_size)
    y = torch.randn(100, output_size)
    
    # Define loss function and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    # Train the model
    for epoch in range(num_epochs):
        # Forward pass
        outputs = model(X)
        loss = criterion(outputs, y)
        
        # Backward and optimize
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # Print progress
        if (epoch+1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')
    
    # Create input example for model signature
    input_example = torch.randn(1, input_size)
    
    # Set up MLflow experiment
    experiment_name = "PyTorch_Model_Example"
    
    # Log the model to MLflow
    run_id = log_pytorch_model(
        model=model,
        model_params=model_params,
        experiment_name=experiment_name,
        run_name="simple_nn_run",
        input_example=input_example,
        tags={"model_type": "regression", "framework": "pytorch"}
    )
    
    print(f"Model logged to MLflow with run_id: {run_id}")
    
    # Load the model back
    loaded_model = mlflow.pytorch.load_model(f"runs:/{run_id}/model")
    
    # Make predictions with the loaded model
    with torch.no_grad():
        test_input = torch.randn(5, input_size)
        predictions = loaded_model(test_input)
        print("Predictions from loaded model:")
        print(predictions)


if __name__ == "__main__":
    main()
