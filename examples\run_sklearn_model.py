import sys
import os
import time

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.mlflow_sklearn_utils import log_sklearn_model
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
import numpy as np
import mlflow
import logging

# Set MLflow logging level to DEBUG to see more information
logging.getLogger("mlflow").setLevel(logging.DEBUG)


if __name__ == "__main__":
    # Set environment variables for system metrics
    os.environ["MLFLOW_ENABLE_SYSTEM_METRICS_LOGGING"] = "true"

    # Set the tracking URI to the MLflow server
    mlflow.set_tracking_uri("http://localhost:5000")
    print(f"MLflow tracking URI: {mlflow.get_tracking_uri()}")
    
    # set experiment
    # 

    # Enable system metrics logging
    mlflow.enable_system_metrics_logging()

    experiment_name = "Test KNC run"
    model = KNeighborsClassifier()
    model_params = {
        "n_neighbors": 3,
        "leaf_size": 25
    }
    # Load Iris dataset
    iris = load_iris()
    X, y = iris.data, iris.target
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # Fit the model
    model.set_params(**model_params)
    model.fit(X_train, y_train)
    predictions = model.predict(X_test)


    # Calculate metrics
    accuracy = accuracy_score(y_test, predictions)
    f1 = f1_score(y_test, predictions, average="weighted")
    precision = precision_score(y_test, predictions, average="weighted")
    recall = recall_score(y_test, predictions, average="weighted")
    metrics = {
        "accuracy": accuracy,
        "f1_score": f1,
        "precision": precision,
        "recall": recall
    }

    # Define model signature
    input_example = X_test[0].reshape(1, -1)

    # Define tags
    tags = {
        "model_type": "KNeighborsClassifier",
        "framework": "sklearn",
        "dataset": "Iris"
    }
    # mlflow.set_experiment(experiment_name)
    # Start MLflow run directly to have more control
    with mlflow.start_run(run_name="KNeighborsClassifier_direct_run") as run:
        # Log parameters
        for param_name, param_value in model_params.items():
            mlflow.log_param(param_name, param_value)

        # Log metrics
        for metric_name, metric_value in metrics.items():
            mlflow.log_metric(metric_name, metric_value)

        # Log tags
        mlflow.set_tags(tags)

        # # Perform CPU-intensive operations to generate system metrics
        # print("Performing CPU-intensive operations to generate system metrics...")
        # for i in range(10):
        #     # Simulate CPU load
        #     start_time = time.time()
        #     while time.time() - start_time < 1:
        #         _ = [i**2 for i in range(10000)]

        #     # Log a metric for each iteration
        #     mlflow.log_metric("iteration", i)
        #     print(f"Completed iteration {i}")

        # Log the model
        mlflow.sklearn.log_model(
            sk_model=model,
            artifact_path="model",
            input_example=input_example
        )

        print(f"Run ID: {run.info.run_id}")
        print(f"Experiment ID: {run.info.experiment_id}")
        print("Check the MLflow UI to see if system metrics are being displayed")

    # Also try with the utility function
    print("\nNow trying with log_sklearn_model function...")
    run_id = log_sklearn_model(
        model=model,
        model_params=model_params,
        experiment_name=experiment_name,
        run_name="KNeighborsClassifier_utility_run",
        metrics=metrics,
        tags=tags,
        input_example=input_example
    )
    print(f"Utility function run ID: {run_id}")