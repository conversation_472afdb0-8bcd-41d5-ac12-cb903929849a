import os
import mlflow
import mlflow.xgboost
import numpy as np
from typing import Dict, Any, Optional, Union, List
from mlflow.models import ModelSignature, infer_signature


def log_xgboost_model(
    model: Any,
    model_params: Dict[str, Any],
    artifact_path: str = "model",
    experiment_name: Optional[str] = None,
    run_name: Optional[str] = None,
    signature: Optional[ModelSignature] = None,
    input_example: Optional[np.ndarray] = None,
    conda_env: Optional[Union[Dict[str, Any], str]] = None,
    registered_model_name: Optional[str] = None,
    code_paths: Optional[List[str]] = None,
    tags: Optional[Dict[str, Any]] = None,
    metrics: Optional[Dict[str, Any]] = None,
    auto_end_run: bool = True
) -> str:
    """
    Log an XGBoost model to MLflow.

    Parameters:
    -----------
    model : Any
        The XGBoost model to log (can be xgboost.Booster, xgboost.XGBModel, or xgboost.XGBRegressor/XGBClassifier)
    model_params : Dict[str, Any]
        Dictionary of model parameters to log
    artifact_path : str, default="model"
        Path within the run artifacts where the model will be saved
    experiment_name : str, optional
        Name of the experiment to use. If not provided, the current active experiment is used.
    run_name : str, optional
        Name of the run to create
    signature : ModelSignature, optional
        Model signature that describes the model's inputs and outputs
    input_example : np.ndarray, optional
        Example input that will be used to infer the model signature if signature is not provided
    conda_env : Union[Dict[str, Any], str], optional
        Either a dictionary representation of a conda environment or path to a conda environment YAML file
    registered_model_name : str, optional
        If provided, the model will be registered with this name in the MLflow Model Registry
    code_paths : List[str], optional
        List of local filesystem paths to Python file dependencies
    tags : Dict[str, Any], optional
        Dictionary of tags to add to the run
    metrics : Dict[str, Any], optional
        Dictionary of metrics to log to the run
    auto_end_run : bool, default=True
        Whether to end the run automatically after logging the model

    Returns:
    --------
    str
        The ID of the MLflow run
    """
    # Set experiment if provided
    if experiment_name:
        mlflow.set_experiment(experiment_name)

    # Start a new run if needed
    active_run = mlflow.active_run()
    if active_run is None:
        with mlflow.start_run(run_name=run_name) as run:
            return _log_model_internal(
                model, model_params, artifact_path, signature, input_example,
                conda_env, registered_model_name, code_paths, tags, metrics,
                auto_end_run, run
            )
    else:
        return _log_model_internal(
            model, model_params, artifact_path, signature, input_example,
            conda_env, registered_model_name, code_paths, tags, metrics,
            auto_end_run, active_run
        )


def _log_model_internal(
    model, model_params, artifact_path, signature, input_example,
    conda_env, registered_model_name, code_paths, tags, metrics, auto_end_run, run
):
    """Internal function to log the model and parameters"""
    # Log model parameters
    for param_name, param_value in model_params.items():
        mlflow.log_param(param_name, param_value)

    # Log any additional tags
    if tags:
        mlflow.set_tags(tags)

    # Log any metrics
    if metrics:
        for metric_name, metric_value in metrics.items():
            mlflow.log_metric(metric_name, metric_value)

    # If signature is not provided but input_example is, infer the signature
    if signature is None and input_example is not None:
        # Get model output for the input example
        # Handle different XGBoost model types
        try:
            # For scikit-learn API models (XGBRegressor, XGBClassifier)
            output = model.predict(input_example)
        except AttributeError:
            # For native Booster objects
            import xgboost as xgb
            dmatrix = xgb.DMatrix(input_example)
            output = model.predict(dmatrix)
        
        # Infer signature
        signature = infer_signature(input_example, output)

    # Log the model
    mlflow.xgboost.log_model(
        xgb_model=model,
        artifact_path=artifact_path,
        conda_env=conda_env,
        code_paths=code_paths,
        signature=signature,
        input_example=input_example,
        registered_model_name=registered_model_name
    )

    # End the run if auto_end_run is True and we started a new run
    if auto_end_run and run.info.run_id == mlflow.active_run().info.run_id:
        mlflow.end_run()

    return run.info.run_id
